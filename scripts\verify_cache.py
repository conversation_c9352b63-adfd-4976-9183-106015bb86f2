import os
import sys
import json
import redis
from dotenv import load_dotenv

# --- Setup Paths and Load Env ---
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)
load_dotenv(os.path.join(project_root, ".env"))

# --- Import Config ---
try:
    from config import config
    from utils.cache import generate_cache_key # Use the same key generator
except ImportError as e:
    print(f"Error importing modules: {e}", file=sys.stderr)
    sys.exit(1)

def check_cache_for_user(data_type_prefix, username):
    """Directly connects to <PERSON><PERSON> and fetches the value for a given key."""
    
    try:
        # Connect to Redis. Ensure REDIS_URL and REDIS_DB are in your .env or config
        redis_client = redis.Redis.from_url(
            config.REDIS_URL,
            db=getattr(config, 'REDIS_DB', 0),
            decode_responses=True # Decode from bytes to string
        )
        print(f"Successfully connected to Redis.")
    except Exception as e:
        print(f"Failed to connect to Redis: {e}")
        return

    # Generate the exact same cache key the refresh script uses
    cache_key = generate_cache_key(data_type_prefix, username)
    print(f"Checking cache for key: '{cache_key}'")

    try:
        # Get the value from Redis
        cached_value_str = redis_client.get(cache_key)
        
        if cached_value_str is None:
            print("\n--- RESULT ---")
            print("No data found in cache for this key.")
            return

        # The data is stored as a JSON string, so we need to parse it
        data = json.loads(cached_value_str)

        print("\n--- RESULT ---")
        # Pretty-print the JSON data
        print(json.dumps(data, indent=2))
        print(f"\nFound {len(data)} item(s) in the cached data.")

    except redis.exceptions.RedisError as e:
        print(f"A Redis error occurred: {e}")
    except json.JSONDecodeError:
        print(f"Error: The value for key '{cache_key}' is not valid JSON.")
        print(f"Raw value: {cached_value_str}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        redis_client.close()


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(f"Usage: python {sys.argv[0]} <username>")
        sys.exit(1)
        
    target_username = sys.argv[1]
    
    # We are checking the 'cms_courses' data
    print("-----------------------------------------")
    print(f"Verifying 'cms_courses' for {target_username}")
    print("-----------------------------------------")
    check_cache_for_user("cms_courses", target_username)